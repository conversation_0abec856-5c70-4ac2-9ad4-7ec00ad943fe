# TicTaps Games - Project Tasks

## Project Architecture Overview

```
PWA -->|auth api| PY
PY -->|auth api| NODE_GAMES
PWA -->|iFrame postMessage: auth token, submit score ID, room ID, opponent score, game ID| GameApp
GameApp -->|SOCKET| NODE_GAMES
NODE_GAMES -->|auth api, submit score api| PY
NODE_GAMES -->|active rooms, scores, timers| REDIS
PY --> DB
```

### Components:

- **PWA**: Progressive Web App (main application)
- **GameApp**: Client-side game application (SvelteKit)
- **NODE_GAMES**: Node.js game server with Socket.IO
- **PY**: Python backend API (authentication & scoring)
- **REDIS**: Cache for active rooms, scores, timers
- **DB**: Database for persistent data

## Project Structure

- `client/` - GameApp (SvelteKit application)
- `server/` - NODE*GAMES (Node.js game server) - \_To be created*

## Development Tasks

### Server Infrastructure & Setup

- [ ] **[SERVER]** Create `server/` directory structure
- [ ] **[SERVER]** Initialize Node.js project with package.json
- [ ] **[SERVER]** Set up Express.js server
- [ ] **[SERVER]** Configure Socket.IO for real-time communication
- [ ] **[SERVER]** Set up in-memory data structures (hash maps for rooms, sessions)
- [ ] **[SERVER]** Create environment configuration (.env setup)
- [ ] **[SERVER]** Set up TypeScript configuration for server
- [ ] **[SERVER]** Create basic server entry point (app.js/index.ts)

### Authentication & Security

- [ ] **[SERVER]** Create auth middleware for validating tokens from PY backend
- [ ] **[SERVER]** Implement auth API client to communicate with Python backend
- [ ] **[SERVER]** Set up token validation for Socket.IO connections
- [ ] **[SERVER]** Create user session management
- [ ] **[SERVER]** Implement secure room access control

### Game Room Management

- [ ] **[SERVER]** Design room data structure using in-memory hash maps
- [ ] **[SERVER]** Implement room creation and joining logic with hash maps
- [ ] **[SERVER]** Create room state management (active players, game state) in memory
- [ ] **[SERVER]** Implement basic room cleanup (manual/timeout-based)
- [ ] **[SERVER]** Add room capacity and validation logic

### Real-time Communication

- [ ] **[SERVER]** Design Socket.IO event structure
- [ ] **[SERVER]** Implement player connection/disconnection handling
- [ ] **[SERVER]** Create game state synchronization
- [ ] **[SERVER]** Implement real-time score updates
- [ ] **[SERVER]** Add game timer management
- [ ] **[SERVER]** Create opponent matching system
- [ ] **[CLIENT]** Implement Socket.IO client integration
- [ ] **[CLIENT]** Create postMessage communication handler for PWA iframe
- [ ] **[CLIENT]** Implement postMessage listener for PWA communication

### Score Management

- [ ] **[SERVER]** Implement score submission to Python backend
- [ ] **[SERVER]** Create score validation and anti-cheat measures
- [ ] **[SERVER]** Set up leaderboard data management
- [ ] **[SERVER]** Implement score history tracking
- [ ] **[SERVER]** Add game result persistence
- [ ] **[CLIENT]** Process submit score ID for score submission
- [ ] **[CLIENT]** Set up score submission queue with submit score ID
- [ ] **[CLIENT]** Process opponent score data for display
- [ ] **[CLIENT]** Implement opponent score tracking and display

### Client-Side Game Framework

- [x] **[CLIENT]** Set up GameApp routing structure with game ID routing
- [x] **[CLIENT]** Create shared UI components library
- [ ] **[CLIENT]** Implement audio system for games
- [ ] **[CLIENT]** Create utility functions and helpers
- [x] **[CLIENT]** Set up game state management (stores)
- [x] **[CLIENT]** Implement game ID routing to specific games

### Generic Game UI Components

- [ ] **[CLIENT]** Create generic countdown timer component
- [ ] **[CLIENT]** Design universal HUD component (score, time, progress)
- [ ] **[CLIENT]** Implement generic end game screen component
- [ ] **[CLIENT]** Create game overlay system for UI integration
- [ ] **[CLIENT]** Design responsive UI components for different screen sizes

### PWA Integration & Authentication

- [ ] **[CLIENT]** Handle auth token reception and validation from PWA
- [ ] **[CLIENT]** Implement auth token storage and refresh logic
- [ ] **[CLIENT]** Create secure postMessage validation
- [ ] **[CLIENT]** Handle room ID for multiplayer game joining
- [ ] **[CLIENT]** Create room state management with received room ID

### Game State & Lifecycle Management

- [ ] **[CLIENT]** Create game state store for received PWA data
- [ ] **[CLIENT]** Implement game lifecycle management within iframe
- [ ] **[CLIENT]** Create error handling for invalid PWA data

### Phaser 3 Game Integration

- [x] **[CLIENT]** Integrate Finger Frenzy game with generic UI components
- [ ] **[CLIENT]** Integrate Bingo game with generic UI components
- [ ] **[CLIENT]** Integrate Matching Mayhem game with generic UI components
- [ ] **[CLIENT]** Integrate Number Sequence game with generic UI components
- [x] **[CLIENT]** Refactor games to use shared countdown timer
- [x] **[CLIENT]** Refactor games to use universal HUD component
- [x] **[CLIENT]** Refactor games to use generic end game screen
- [ ] **[CLIENT]** Implement game-specific scoring interfaces
- [ ] **[CLIENT]** Add Socket.IO integration to each game for real-time updates

### Server-Side Game Logic

- [ ] **[SERVER]** Implement Finger Frenzy timer and scoring logic
- [ ] **[SERVER]** Implement Bingo timer and scoring logic
- [ ] **[SERVER]** Implement Matching Mayhem timer and scoring logic
- [ ] **[SERVER]** Implement Number Sequence timer and scoring logic
- [ ] **[SERVER]** Create game-specific validation rules
- [ ] **[SERVER]** Implement anti-cheat measures for each game type
- [ ] **[SERVER]** Add game duration management and timeouts
- [ ] **[SERVER]** Create game result calculation and comparison logic

## Technical Specifications

### Server Technologies

- Node.js with Express.js
- Socket.IO for real-time communication
- Redis for session and game state management
- TypeScript for type safety
- JWT for authentication validation

### Client Technologies

- SvelteKit for the GameApp framework
- Phaser 3 for game engines (4 existing games)
- Socket.IO client for real-time communication
- TypeScript for type safety
- Tailwind CSS for styling

### Game Portfolio

- **Finger Frenzy**: Fast-paced finger tapping game
- **Bingo**: Classic bingo with multiplayer features
- **Matching Mayhem**: Memory/matching card game
- **Number Sequence**: Pattern recognition and sequence game

### Communication Protocols

- REST API for auth validation with Python backend
- Socket.IO for real-time game communication
- PostMessage API for iframe communication with PWA

### PWA to GameApp Data Flow (via postMessage)

- **Auth Token**: JWT token for user authentication
- **Submit Score ID**: Unique identifier for score submission tracking
- **Room ID**: Multiplayer room identifier (if joining existing room)
- **Opponent Score**: Current opponent's score (if applicable)
- **Game ID**: Identifier for routing to specific game component

## Next Immediate Steps

### Server Setup Priority

1. Create server directory structure
2. Initialize Node.js project with dependencies
3. Set up basic Express server with Socket.IO
4. Set up in-memory data structures (hash maps for rooms and sessions)
5. Create initial auth middleware
6. Implement basic room management with hash maps

### Client Integration Priority

1. Create generic UI components (countdown, HUD, end game)
2. Set up game routing for the 4 Phaser 3 games
3. Integrate first game (Finger Frenzy) with generic UI
4. Implement server-side timer and scoring for Finger Frenzy
5. Test end-to-end multiplayer functionality with one game
6. Replicate integration pattern for remaining 3 games

## Backlog (Future Enhancements)

### Redis Integration (For Production/Persistence)

- [ ] **[SERVER]** Set up Redis connection and configuration
- [ ] **[SERVER]** Migrate room data structure from hash maps to Redis

## Notes

- **Current Approach**: Using in-memory hash maps for quick development and testing
- **Future Migration**: Redis integration planned for production persistence and scaling
- Ensure all communications are secure and validated
- Implement proper error handling and logging
- Consider scalability for multiple concurrent games
- Plan for graceful degradation if services are unavailable
