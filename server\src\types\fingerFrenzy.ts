
import type { GameState } from './game';
/**
 * Represents a single grid block in the game
 */
export interface GridBlock {
  id: string;
  row: number;
  col: number;
  isActive: boolean;
  index: number;
}

/**
 * Game data structure for Finger Frenzy
 */
export interface FingerFrenzyGameData {
  grid: GridBlock[];
  activeBlockCount: number;
}

/**
 * Result of a tile tap action
 */
export interface TileTapResult {
  success: boolean;
  isCorrect: boolean;
  points: number;
  newScore: number;
  newLives: number;
  newBlock?: GridBlock | null;
  gameEnded: boolean;
  message?: string;
}

/**
 * Grid state for client synchronization
 */
export interface GridState {
  blocks: GridBlock[];
  activeCount: number;
}

/**
 * Tile states for client updates
 */
export interface TileStates {
  [tileId: string]: {
    isActive: boolean;
  };
}

/**
 * Game initialization result
 */
export interface GameInitResult {
  success: boolean;
  gameState?: GameState;
  message?: string;
}

/**
 * Reasons for ending a Finger Frenzy game
 */
export type FingerFrenzyEndReason = 'timeout' | 'no_lives' | 'manual';

/**
 * Socket event data structures
 */
export interface GameStartData {
  roomId: string;
  gameId: string;
}

export interface GameEndData {
  roomId: string;
  gameId: string;
  reason?: FingerFrenzyEndReason;
}

export interface GameActionData {
  roomId: string;
  gameId: string;
  action: {
    type: string;
    data: any;
  };
}

export interface TileTapActionData extends GameActionData {
  action: {
    type: 'tile_tap';
    data: {
      tileId: string;
      reactionTime: number;
      clickTime?: number;
    };
  };
}
