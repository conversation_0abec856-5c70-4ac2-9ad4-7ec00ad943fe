name: Deploy Server to Cloud Run

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'server/**'
      - '.github/workflows/deploy-server.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to build for'
        required: true
        default: 'develop'
        type: choice
        options:
          - develop
          - production

env:
  REGISTRY: europe-docker.pkg.dev
  SERVICE_NAME: svc-games-server
  REGION: europe-west1
  REPOSITORY: tictaps

jobs:
  # Determine environment based on branch
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
    steps:
      - uses: actions/checkout@v4

      - name: Set environment
        id: set-env
        run: |
          # Use manual input if provided, otherwise determine by branch
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "environment=develop" >> $GITHUB_OUTPUT
          fi

  # Build and push server image
  build-server-image:
    needs: [setup]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Google Artifact Registry
        run: gcloud auth configure-docker ${{ env.REGISTRY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./server
          file: ./server/Dockerfile
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ github.sha }}
            ${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ needs.setup.outputs.environment }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:buildcache,mode=max
          platforms: linux/amd64

  # Deploy server to Cloud Run
  deploy-server:
    needs: [setup, build-server-image]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Deploy Server to Cloud Run
        run: |-
          gcloud run deploy ${{ needs.setup.outputs.environment }}-${{ env.SERVICE_NAME }} \
            --region ${{ env.REGION }} \
            --image ${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ github.sha }} \
            --platform managed \
            --allow-unauthenticated