import { writable } from 'svelte/store';

export interface GameState {
	score: number;
	time: number;
	totalTime: number;
	maxLives: number;
	lives: number;
	isLoading: boolean;
	loadingProgress: number;
	isCountdown: boolean;
	isPlaying: boolean;
	isPaused: boolean;
	gameOver: boolean;
	opponentScore: number | null;
	roomId: string | null;
	authToken: string | null;
	submitScoreId: string | null;
	gameId: string | null;
}

const initialState: GameState = {
	score: 0,
	time: 30,
	totalTime: 30,
	maxLives: 3,
	lives: 3,
	isLoading: true,
	loadingProgress: 0,
	isCountdown: false,
	isPlaying: false,
	isPaused: false,
	gameOver: false,
	opponentScore: null,
	roomId: null,
	authToken: null,
	submitScoreId: null,
	gameId: null
};

export const gameState = writable<GameState>(initialState);

// Helper functions for updating game state
export const gameActions = {
	updateLoadingProgress: (progress: number) => {
		// console.log("Updating loading progress to", progress);
		gameState.update(state => ({ ...state, loadingProgress: progress }));
	},
	
	updateScore: (score: number) => {
		gameState.update(state => ({ ...state, score }));
	},
	
	updateTime: (time: number) => {
		gameState.update(state => ({ ...state, time }));
	},
	
	updateLives: (lives: number) => {
		gameState.update(state => ({ ...state, lives }));
	},

	preloadComplete: () => {
		gameState.update(state => ({ ...state, isLoading: false }));
	},

	initGame: () => {
		gameState.update(state => ({ 
			...state, 
			isCountdown: true,
			isPlaying: false, 
			isPaused: false, 
			gameOver: false  }));
	},
	
	startGame: () => {
		gameState.update(state => ({ 
			...state, 
			isPlaying: true, 
		}));
	},
	
	pauseGame: () => {
		gameState.update(state => ({ ...state, isPaused: true }));
	},
	
	resumeGame: () => {
		gameState.update(state => ({ ...state, isPaused: false }));
	},
	
	endGame: () => {
		gameState.update(state => ({ 
			...state, 
			isPlaying: false, 
			gameOver: true 
		}));
	},
	
	resetGame: () => {
		gameState.set(initialState);
	},
	
	setOpponentScore: (opponentScore: number) => {
		gameState.update(state => ({ ...state, opponentScore }));
	},
	
	setRoomData: (roomId: string, authToken: string, submitScoreId: string) => {
		gameState.update(state => ({ 
			...state, 
			roomId, 
			authToken, 
			submitScoreId 
		}));
	},
	
	setGameId: (gameId: string) => {
		gameState.update(state => ({ ...state, gameId }));
	}
};
