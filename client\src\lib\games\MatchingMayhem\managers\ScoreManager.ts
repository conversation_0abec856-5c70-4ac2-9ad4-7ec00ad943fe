import * as Phaser from 'phaser';
import type { ScoreConfig, ScoreAnimationConfig } from '../types/types';

/**
 * ```
 * const scoreManager = new ScoreManager(scene, {
 *   initialScore: 0,
 *   fontSize: '80px',
 *   scoreColor: '#33DDFF'
 * });
 * 
 * scoreManager.createUI(x, y);
 * scoreManager.addPoints(10, playerX, playerY);
 * ```
 */
export default class ScoreManager {
  private scene: Phaser.Scene;
  private config: Required<ScoreConfig>;
  private score: number;
  
  // UI Elements
  private scoreText?: Phaser.GameObjects.Text;
  private scoreLabel?: Phaser.GameObjects.Text;
  private container?: Phaser.GameObjects.Container;
  
  // Events
  private events: Phaser.Events.EventEmitter;

  constructor(scene: Phaser.Scene, config: ScoreConfig = {}) {
    this.scene = scene;
    this.events = new Phaser.Events.EventEmitter();
    
    // Set default configuration
    this.config = {
      initialScore: config.initialScore ?? 0,
      fontFamily: config.fontFamily ?? 'Arial',
      fontSize: config.fontSize ?? '80px',
      labelFontSize: config.labelFontSize ?? '28px',
      scoreColor: config.scoreColor ?? '#33DDFF',
      labelColor: config.labelColor ?? '#FFFFFF',
      animationColor: config.animationColor ?? '#ffff00',
      animationDuration: config.animationDuration ?? 800
    };
    
    this.score = this.config.initialScore;
  }

  /**
   * Create the score UI elements at the specified position
   */
  public createUI(x: number, y: number, parentContainer?: Phaser.GameObjects.Container): void {
    // Create container for score elements
    this.container = this.scene.add.container(0, 0);
    
    // Create score label
    this.scoreLabel = this.scene.add.text(x, y - 30, 'Total Point', {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.labelFontSize,
      fontStyle: 'bold',
      color: this.config.labelColor
    }).setOrigin(0.5);
    
    // Create score text
    this.scoreText = this.scene.add.text(x, y + 30, this.score.toString(), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: 'bold',
      color: this.config.scoreColor
    }).setOrigin(0.5);
    
    // Add to container
    this.container.add([this.scoreLabel, this.scoreText]);
    
    // Add to parent container if provided
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }

  /**
   * Add points to the score with optional animation
   */
  public addPoints(points: number, animationConfig?: ScoreAnimationConfig): void {
    this.score += points;
    this.updateScoreDisplay();
    
    // Create flying score animation if config provided
    if (animationConfig) {
      this.createScoreAnimation(animationConfig);
    }
    
    // Emit score change event
    this.events.emit('scoreChanged', this.score, points);
  }

  /**
   * Subtract points from the score with optional animation
   */
  public subtractPoints(points: number, animationConfig?: ScoreAnimationConfig): void {
    this.score = Math.max(0, this.score - points);;
    this.updateScoreDisplay();

    // Emit score change event
    this.events.emit('scoreChanged', this.score, -points);
  }

  /**
   * Set the score to a specific value
   */
  public setScore(newScore: number): void {
    const oldScore = this.score;
    this.score = newScore;
    this.updateScoreDisplay();
    
    // Emit score change event
    this.events.emit('scoreChanged', this.score, this.score - oldScore);
  }

  /**
   * Get the current score
   */
  public getScore(): number {
    return this.score;
  }

  /**
   * Reset score to initial value
   */
  public reset(): void {
    this.score = this.config.initialScore;
    this.updateScoreDisplay();
    this.events.emit('scoreReset', this.score);
  }

  /**
   * Update the score display text
   */
  private updateScoreDisplay(): void {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }

  /**
   * Create animated flying score text
   */
  private createScoreAnimation(config: ScoreAnimationConfig): void {
    const animationText = this.scene.add.text(
      config.startX,
      config.startY,
      `+${config.points}`,
      {
        fontFamily: this.config.fontFamily,
        fontSize: '24px',
        color: config.color ?? this.config.animationColor,
        stroke: '#000000',
        strokeThickness: 3
      }
    );
    animationText.setOrigin(0.5);

    // Animate the score text
    this.scene.tweens.add({
      targets: animationText,
      y: config.startY - 50,
      alpha: 0,
      scale: 1.2,
      duration: config.duration ?? this.config.animationDuration,
      ease: 'Power2',
      onComplete: () => {
        animationText.destroy();
      }
    });
  }
  
  /**
   * Subscribe to score events
   */
  public on(event: 'scoreChanged' | 'scoreReset', callback: (...args: any[]) => void): void {
    this.events.on(event, callback);
  }

  /**
   * Unsubscribe from score events
   */
  public off(event: 'scoreChanged' | 'scoreReset', callback: (...args: any[]) => void): void {
    this.events.off(event, callback);
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    this.events.removeAllListeners();
    
    if (this.container) {
      this.container.destroy();
    }
    
    this.scoreText = undefined;
    this.scoreLabel = undefined;
    this.container = undefined;
  }
}
