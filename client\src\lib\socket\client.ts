import { gameState, gameActions } from '../stores';
import { PUBLIC_SOCKET_SERVER_URL } from '$env/static/public';
import io from 'socket.io-client';

export class SocketClient {
	private socket: any = null;
	private serverUrl: string;

	constructor() {
		this.serverUrl = PUBLIC_SOCKET_SERVER_URL;
		console.log(this.serverUrl)
	}

	async connect(authToken: string, callbacks?: any): Promise<void> {
		return new Promise((resolve, reject) => {
			this.socket = io(this.serverUrl, {
				auth: {
					token: authToken
				}
			});

			this.socket.on('connect', () => {
				console.log('Connected to game server');
				this.setupEventListeners(callbacks);

				resolve();
			});

			this.socket.on('connect_error', (error: any) => {
				console.error('Connection error:', error);
				reject(error);
			});
		});
	}

	private setupEventListeners(callbacks?: any) {
		if (!this.socket) return;

		this.socket.on('gameUpdate', (data: any) => {
			// Handle game state updates from server
			console.log('Game update received:', data);
		});

		this.socket.on('opponentScore', (score: number) => {
			gameActions.setOpponentScore(score);
		});

		this.socket.on('roomJoined', (roomData: any) => {
			console.log('Joined room:', roomData);
		});

		this.socket.on('playerJoined', (playerData: any) => {
			console.log('Player joined:', playerData);
		});

		this.socket.on('playerLeft', (playerData: any) => {
			console.log('Player left:', playerData);
		});

		this.socket.on('gameStarted', () => {
			gameActions.startGame();
		});

		this.socket.on('gameEnded', (results: any) => {
			gameActions.endGame();
			console.log('Game ended:', results);
		});

		// Generic game event listeners
		this.socket.on('started', (data: any) => {
			console.log('Game started:', data);
			// Data includes: gameId, gameState, gridState, message
			gameActions.startGame();
		});

		this.socket.on('ended', (data: any) => {
			console.log('Game ended:', data);
			// Data includes: gameId, reason, finalScore, playerId

			callbacks.onGameComplete(data.finalScore);
		});

		this.socket.on('action_result', (data: any) => {
			console.log('Game action result:', data);
			// Data includes: gameId, actionType, playerId, data

			// Handle different callbacks based on actionType
			if (data.actionType === 'tile_tap') {
				console.log('Tile tap result:', data.data);
				callbacks.onScoreUpdate(data.data.newScore);
			}
		});

		this.socket.on('score', (data: any) => {
			console.log('Score update from server:', data);

			gameActions.updateScore(data.score);
		});

		this.socket.on('timer_tick', (data: any) => {
			console.log('Timer update from server:', data);

			gameActions.updateTime(data.duration);
		});

		this.socket.on('error', (data: any) => {
			console.error('Game error:', data);
			// Data includes: gameId, message
		});
	}

	joinRoom(roomId: string) {
		if (this.socket) {
			this.socket.emit('joinRoom', { roomId });
		}
	}

	createRoom(gameId: string) {
		if (this.socket) {
			this.socket.emit('createRoom', { gameId });
		}
	}

	sendScore(score: number, roomId?: string) {
		if (this.socket) {
			this.socket.emit('submit_score', {
				score,
				roomId: roomId || 'default-room',
				playerId: 'player-1', // TODO: Get actual player ID
				gameType: 'finger-frenzy' // TODO: Get actual game type
			});
		}
	}

	sendGameEvent(eventType: string, data: any) {
		if (this.socket) {
			this.socket.emit('game_action', {
				action: eventType,
				gameData: data,
				gameId: data.gameId || 'finger-frenzy',
				roomId: data.roomId || 'default-room',
				playerId: 'player-1' // TODO: Get actual player ID
			});
		}
	}

	// Generic game methods
	startGame(gameId: string, roomId: string = 'default-room') {
		if (this.socket) {
			this.socket.emit('start', {
				gameId,
				roomId,
			});
		}
	}

	endGame(gameId: string, roomId: string = 'default-room', reason: string = 'manual') {
		if (this.socket) {
			this.socket.emit('end', {
				gameId,
				roomId,
				reason
			});
		}
	}

	sendGameAction(gameId: string, roomId: string, actionType: string, actionData: any) {
		if (this.socket) {
			this.socket.emit('action', {
				gameId,
				roomId,
				action: {
					type: actionType,
					data: actionData
				}
			});
		}
	}

	// Convenience methods for specific actions
	sendTileTap(gameId: string, roomId: string, tileId: string, reactionTime?: number) {
		this.sendGameAction(gameId, roomId, 'tile_tap', {
			tileId,
			reactionTime: reactionTime || 0,
			clickTime: Date.now()
		});
	}

	disconnect() {
		if (this.socket) {
			this.socket.disconnect();
			this.socket = null;
		}
	}

	isConnected(): boolean {
		return this.socket?.connected ?? false;
	}

	// Add custom event listener
	addCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.on(event, callback);
		}
	}

	// Remove custom event listener
	removeCustomEventListener(event: string, callback: (data: any) => void) {
		if (this.socket) {
			this.socket.off(event, callback);
		}
	}
}

// Singleton instance
export const socketClient = new SocketClient();
