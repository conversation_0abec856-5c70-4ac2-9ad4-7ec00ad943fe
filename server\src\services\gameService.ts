import { GameType, GameState, GameAction, GameResults } from '../types/game';
import { logger } from '../utils/logger';
import { DEFAULT_GAME_DURATION } from '../utils/constants';
import type { Server, Socket } from 'socket.io';

export class GameService {
  private gameStates: Map<string, GameState> = new Map();
  private gameTimers: Map<string, { timer: NodeJS.Timeout, duration: number }> = new Map();

  createGameState(roomId: string, gameType: GameType, initialLives: number): GameState {
    const gameState: GameState = {
      gameType,
      status: 'waiting',
      score: 0,
      scoreAry: [],
      lives: initialLives
    };

    this.gameStates.set(roomId, gameState);
    logger.info(`Game state created for room ${roomId}, game type: ${gameType}${initialLives ? `, lives: ${initialLives}` : ''}`);
    return gameState;
  }

  getGameState(roomId: string): GameState | undefined {
    return this.gameStates.get(roomId);
  }

  startGame(roomId: string , socket: Socket): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot start game: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'waiting') {
      logger.warn(`Cannot start game: game in room ${roomId} is not in waiting state`);
      return false;
    }

    gameState.status = 'active';
    gameState.startTime = Date.now();
    
    // Set up game timer
    const duration = DEFAULT_GAME_DURATION[gameState.gameType];
    // const timer = setTimeout(() => {
    //   this.endGame(roomId, 'timeout');
    // }, duration);

    const timer = setInterval(() => {
      console.log(`Game timer tick in room ${roomId}`);
      const timerObj = this.gameTimers.get(roomId);
      if (timerObj) {
        timerObj.duration--;

        this.gameTimers.set(roomId, timerObj);
        logger.debug(`Game timer tick in room ${roomId}, remaining duration: ${timerObj.duration}ms`);

        socket.emit('timer_tick', {
          duration: timerObj.duration
        });

        // If duration reaches zero, end the game
        if (timerObj.duration <= 0) {
          logger.info(`Game timer ended in room ${roomId}`);
          const results = this.endGame(roomId, 'timeout');

          if (results) {
            logger.info(`Game ended in room ${roomId}, reason: ${results.endReason}`);
            socket.emit('ended', results);
          }
        }
      }
    }, 1000);

    this.gameTimers.set(roomId, { timer, duration });
    
    logger.info(`Game started in room ${roomId}, duration: ${duration}ms`);
    return true;
  }

  endGame(roomId: string, reason: 'completed' | 'timeout' | 'disconnection' | 'forfeit'): GameResults | null {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot end game: no game state for room ${roomId}`);
      return null;
    }

    // Clear timer if exists
    const gameTimer = this.gameTimers.get(roomId);
    if (gameTimer) {
      clearInterval(gameTimer.timer);
      this.gameTimers.delete(roomId);
    }

    gameState.status = 'ended';

    const results: GameResults = {
      gameType: gameState.gameType,
      score: gameState.score,
      endReason: reason,
    };

    logger.info(`Game ended in room ${roomId}, reason: ${reason}`);
    return results;
  }

  updateScore(roomId: string, score: number, action: "add" | "subtract"): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot update score: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot update score: game in room ${roomId} is not active`);
      return false;
    }

    switch (action) {
      case "add":
        gameState.score += score;
        gameState.scoreAry.push(score);
        break;
      case "subtract":
        const actual = Math.min(score, gameState.score);

        gameState.score -= actual;
        gameState.scoreAry.push(-actual);
        break;
      default:
        logger.warn(`Cannot update score: invalid action ${action}`);
        return false;
    }

    logger.info(`Score updated  ${gameState.score}`);
    return true;
  }

  updateLives(roomId: string, lives: number): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot update lives: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot update lives: game in room ${roomId} is not active`);
      return false;
    }

    gameState.lives = lives;
    logger.info(`Lives updated in room ${roomId}: ${lives}`);
    return true;
  }

  deductLife(roomId: string): { newLives: number; gameEnded: boolean } {
    const gameState = this.gameStates.get(roomId);
    if (!gameState || gameState.lives === undefined) {
      return { newLives: 0, gameEnded: false };
    }

    const newLives = Math.max(0, gameState.lives - 1);
    this.updateLives(roomId, newLives);

    const gameEnded = newLives <= 0;
    if (gameEnded) {
      this.endGame(roomId, 'completed'); // or 'dead' if we add that to the type
    }

    return { newLives, gameEnded };
  }

  getLives(roomId: string): number {
    const gameState = this.gameStates.get(roomId);
    return gameState?.lives || 0;
  }

  processGameAction(roomId: string,action: GameAction): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot process action: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot process action: game in room ${roomId} is not active`);
      return false;
    }

    // Basic action validation (extend based on game type)
    if (!this.validateAction(gameState.gameType, action)) {
      logger.warn(`Invalid action in room ${roomId}:`, action);
      return false;
    }

    logger.debug(`Action processed in room ${roomId}:`, action.type);
    return true;
  }

  deleteGameState(roomId: string): boolean {
    const gameTimer = this.gameTimers.get(roomId);
    if (gameTimer) {
      clearTimeout(gameTimer.timer);
      this.gameTimers.delete(roomId);
    }

    const deleted = this.gameStates.delete(roomId);
    if (deleted) {
      logger.info(`Game state deleted for room ${roomId}`);
    }
    return deleted;
  }

  private validateAction(gameType: GameType, action: GameAction): boolean {
    // Basic validation
    if (!action.type || typeof action.timestamp !== 'number') {
      return false;
    }

    // Game-specific validation
    switch (gameType) {
      case 'finger-frenzy':
        return ['tile_tap'].includes(action.type);
      case 'bingo':
        return [''].includes(action.type);
      case 'matching-mayhem':
        return [''].includes(action.type);
      case 'number-sequence':
        return [''].includes(action.type);
      default:
        return true;
    }
  }
}

export const gameService = new GameService();
export default gameService;
