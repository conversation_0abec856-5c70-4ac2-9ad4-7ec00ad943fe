import * as Phaser from 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';
import GameConfig from './config/GameConfig';
import type { SocketClient } from '$lib/socket';

// FingerFrenzy game integration
export interface FingerFrenzyConfig {
	gameId: string;
	containerId: string;
	socketClient?: SocketClient;
	roomId?: string;
	// playerId?: string;

	onScoreUpdate?: (score: number) => void;
	onGameComplete?: (finalScore: number) => void;
}

export class FingerFrenzyGame {
	private config: FingerFrenzyConfig;
	private gameInstance: Phaser.Game | null = null;

	constructor(config: FingerFrenzyConfig) {
		this.config = config;
	}

	async init() {
		// Initialize Phaser 3 game instance
		console.log('Initializing FingerFrenzy game...');
		// TODO: Implement Phaser 3 game initialization

		// Game configuration
		const _config: Phaser.Types.Core.GameConfig = {
			type: Phaser.AUTO,
			width: 540,
			height: 960,
			backgroundColor: '#0E0F1E',
			parent: 'game-container',
			scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
			physics: {
			default: 'arcade',
			arcade: {
				gravity: { x: 0, y: 0 },
				debug: false
			}
			},
			scale: {
				mode: Phaser.Scale.EXPAND,
				autoCenter: Phaser.Scale.CENTER_BOTH,
			},
			render: {
				antialias: true,
				pixelArt: false,
				roundPixels: true,
				powerPreference: 'high-performance'
			},
			input: {
				activePointers: 3,
				windowEvents: false
			},
			dom: {
				createContainer: true
			}
		};

		this.gameInstance = new Phaser.Game(_config);

		// Pass game config (including socket client) to all scenes
		if (this.gameInstance) {
			this.gameInstance.registry.set('gameConfig', this.config);
		}
	}

	start() {
		console.log('Starting FingerFrenzy game...');
		// TODO: Start game logic

		this.gameInstance?.scene.start('GameScene');
	}

	pause() {
		console.log('Pausing FingerFrenzy game...');
		// TODO: Pause game logic
	}

	resume() {
		console.log('Resuming FingerFrenzy game...');
		// TODO: Resume game logic
	}

	destroy() {
		console.log('Destroying FingerFrenzy game...');
		if (this.gameInstance) {
			// TODO: Clean up Phaser 3 instance
			this.gameInstance = null;
		}
	}

	getCurrentScore(): number {
		// TODO: Return current game score
		return 0;
	}
}
