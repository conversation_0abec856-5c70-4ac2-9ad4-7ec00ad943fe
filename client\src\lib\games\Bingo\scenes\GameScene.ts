import * as Phaser from 'phaser';
import BingoCell from '../objects/BingoCell';
import RightNumber from '../objects/RightNumber';
import ScoreManager from '../managers/ScoreManager';
import TimerManager from '../managers/TimerManager';
import LivesManager from '../managers/LivesManager';
import TimerBar<PERSON> from '../ui/TimerBarUI';
import SeededRandom from '../utils/KeygenUtil';
import type {
  BingoC<PERSON>umn,
  BingoCellData,
  CalledNumber,
  WinResult,
} from '../types/BingoTypes';
import { 
  BINGO_CONSTANTS, 
  DEFAULT_PATTERN_SCORES, 
  PATTERN_DISPLAY_NAMES 
} from '../config/GameConfig';

/**
 * Equivalent to GameController in Unity
 */
export default class GameScene extends Phaser.Scene {
  private static readonly BINGO_COLUMNS = BINGO_CONSTANTS.COLUMNS;
  private static readonly COLUMN_RANGES = BINGO_CONSTANTS.COLUMN_RANGES;
  private static readonly FREE_SPACE_POSITION = BINGO_CONSTANTS.FREE_SPACE_POSITION;

  // Game state properties
  public gameEnd!: boolean;
  // private rightTurnCount!: number;

  private UIContainer!: Phaser.GameObjects.Container;

  // Bingo-specific properties
  private bingoCard!: BingoCellData[][];
  private calledNumbers!: CalledNumber[];
  private availableNumbers!: number[];
  private possibleNumbers!: number[];
  private currentCallOrder!: number;

  // Game objects
  private bgoCells!: BingoCell[];
  private rightNumbers!: RightNumber[];
  private rightPositions!: { x: number; y: number }[];

  // UI elements
  private countdownPanel!: Phaser.GameObjects.Container;
  private countdownText!: Phaser.GameObjects.Image;

  private scoreManager!: ScoreManager;
  private timerManager!: TimerManager;
  private livesManager!: LivesManager;
  private timerBarUI!: TimerBarUI;

  // Timers and events
  // private isRightItemScheduled: boolean;

  // Add camera dimensions for responsive layout
  private cameraWidth!: number;
  private cameraHeight!: number;
  private centerX!: number;
  private centerY!: number;

  // Seed-based random number generator
  private rng: SeededRandom;
  private gameSeed: number;

  constructor(seed?: number) {
    super('GameScene');
    // this.isRightItemScheduled = false;

    // Initialize seeded random number generator
    this.gameSeed = seed ?? Date.now();
    this.rng = new SeededRandom(this.gameSeed);

    console.log(`Bingo Game initialized with seed: ${this.gameSeed}`);
  }

  init(){
    
    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });

    this.timerManager = new TimerManager(this, {
      duration: BINGO_CONSTANTS.GAME_DURATION,
      warningThreshold: 5
    });

    this.livesManager = new LivesManager(this, {
      initialLives: 3
    });

    this.timerBarUI = new TimerBarUI(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });

    // Set up timer events
    this.timerManager.on('timeUp', () => this.endGame());
    this.timerManager.on('timerUpdate', (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });

    this.livesManager.on('heartDeducted', (lives) => {
      if (lives === 0) {
        this.endGame();
      }
    });
  }

  create() {
    // Set camera dimensions and center points
    this.cameraWidth = this.cameras.main.width;
    this.cameraHeight = this.cameras.main.height;
    this.centerX = this.cameraWidth / 2;
    this.centerY = this.cameraHeight / 2;

    // Initialize game variables
    this.gameEnd = false;
    // this.rightTurnCount = 0;
    this.bgoCells = [];
    this.rightNumbers = [];

    // Initialize bingo-specific data
    this.calledNumbers = [];
    this.currentCallOrder = 0;
    this.availableNumbers = [];
    this.initializePossibleNumbers();

    // Calculate right positions
    this.rightPositions = this.calculateRightPositions();

    // Setup UI elements
    this.createBackground();
    this.createUI();

    // TESTING: Skip countdown and start game immediately
    // this.initializeGame();
    this.startCountdown();
  }

  shutdown() {
    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }

    if (this.timerManager) {
      this.timerManager.destroy();
    }

    if (this.livesManager) {
      this.livesManager.destroy();
    }

    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }
  }

  /**
   * Create game background - using game_bg.png
   */
  createBackground() {
    // Add the game background image
    this.add.image(0, 0, 'game_background')
        .setOrigin(0, 0)
        .setDisplaySize(this.cameraWidth, this.cameraHeight);
  }

  /**
   * Create UI elements
   */
  createUI() {
    const { width, height } = this.cameras.main;
    // // Create score display
    // this.createScoreDisplay();

    // // Create game timer
    // this.createGameTimer();

    this.UIContainer = this.add.container(0, 0);
    // this.gamePanel.add(timerContainer);

    // Create timer bar UI
    this.timerBarUI.create(this.UIContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);

    // Create score display
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 120, this.UIContainer);
    
    // Create lives display
    this.livesManager.createUI(width / 2, timerBarY + 50, this.UIContainer);

    // Create countdown overlay (initially hidden)
    this.createCountdownOverlay();

    // Hide UI elements initially - they will be shown after countdown
    this.hideGameUI();
  }

  /**
   * Create countdown overlay (initially hidden)
   * Using countdown images like in matching mayhem
   */
  createCountdownOverlay() {
    // Create countdown panel container
    this.countdownPanel = this.add.container(0, 0);
    this.countdownPanel.setDepth(2); // Ensure countdown panel is above everything

    // Add semi-transparent overlay
    const overlay = this.add.rectangle(
      0, 0, 
      this.cameraWidth, 
      this.cameraHeight, 
      0x000000, 0.7
    ).setOrigin(0, 0);
    this.countdownPanel.add(overlay);

    // Create countdown image (initially hidden, will be updated during countdown)
    this.countdownText = this.add.image(
      this.centerX,
      this.centerY,
      'countdown-3'
    ).setScale(0).setOrigin(0.5);
    this.countdownPanel.add(this.countdownText);
  }

  /**
   * Start countdown sequence
   * Using image-based countdown like in matching mayhem
   */
  async startCountdown() {
    // Show countdown panel
    this.countdownPanel.visible = true;

    // Countdown sequence with images
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];

    for (let i = 0; i < countdownImages.length; i++) {
      await this.playCountdownStep(countdownImages[i], i === countdownImages.length - 1);
    }

    // Hide countdown panel and start game
    this.countdownPanel.visible = false;
    this.showGameUI();
    this.initializeGame();
  }

  /**
   * Play a single step of the countdown animation
   */
  private playCountdownStep(texture: string, isGo: boolean): Promise<void> {
    return new Promise((resolve) => {
      // Update image texture
      this.countdownText.setTexture(texture);
      this.countdownText.setScale(0);

      // Play appropriate sound
      try {
        this.sound.play(isGo ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      // Scale up animation
      this.tweens.add({
        targets: this.countdownText,
        scale: 0.2, // Same scale as matching mayhem
        duration: 300,
        ease: 'Back.easeOut',
        onComplete: () => {
          // Hold for a moment, then scale down
          this.time.delayedCall(700, () => {
            this.tweens.add({
              targets: this.countdownText,
              scale: 0,
              duration: 300,
              ease: 'Back.easeIn',
              onComplete: () => resolve()
            });
          });
        }
      });
    });
  }

  /**
   * Initialize the game after countdown
   */
  initializeGame() {
    // Generate standard bingo card
    this.bingoCard = this.generateBingoCard();

    // Create bingo board UI
    this.createBingoBoard();

    // Start game timer
    this.startGameTimer();

    // Set initial right turn count using seeded random
    // this.rightTurnCount = this.rng.between(2, 4);

    // Add first right number after delay
    this.time.delayedCall(BINGO_CONSTANTS.INITIAL_CALLOUT_DELAY, () => {
      this.addRightItem();
    });
  }

  private initializePossibleNumbers(): void {
    this.possibleNumbers = [];

    for (let i = 1; i <= 75; i++) {
      this.possibleNumbers.push(i);
    }

    // this.rng.shuffle(this.possibleNumbers);
  }

  private generateBingoCard(): BingoCellData[][] {
    const card: BingoCellData[][] = [];

    // Initialize 5x5 grid
    for (let row = 0; row < 5; row++) {
      card[row] = [];
      for (let col = 0; col < 5; col++) {
        const column = GameScene.BINGO_COLUMNS[col];

        // Handle center FREE space
        if (row === GameScene.FREE_SPACE_POSITION.row && col === GameScene.FREE_SPACE_POSITION.col) {
          card[row][col] = {
            column,
            number: 0, // FREE space has no number
            row,
            col,
            isFree: true
          };
        } else {
          // Generate unique number for this column using seeded random
          const availableInColumn = this.getAvailableNumbersForColumn(column, card);
          const randomIndex = this.rng.between(0, availableInColumn.length - 1);
          const number = availableInColumn[randomIndex];

          // Remove the number from possible numbers
          this.possibleNumbers.splice(number, 1);
          // Add the number to available numbers
          this.availableNumbers.push(number);

          card[row][col] = {
            column,
            number,
            row,
            col,
            isFree: false
          };
        }
      }
    }

    // Add 10 numbers from possible to available - balance gamee
    for (let i = 0; i < 10; i++) {
      const randomIndex = this.rng.between(0, this.possibleNumbers.length - 1);
      const number = this.possibleNumbers[randomIndex];
      this.possibleNumbers.splice(randomIndex, 1);
      this.availableNumbers.push(number);
    }

    // Shuffle available numbers
    this.rng.shuffle(this.availableNumbers);

    return card;
  }

  /**
   * Get available numbers for a specific column that haven't been used yet
  */
  private getAvailableNumbersForColumn(column: BingoColumn, currentCard: BingoCellData[][]): number[] {
    const range = GameScene.COLUMN_RANGES[column];
    const usedNumbers = new Set<number>();

    // Collect already used numbers in this column
    for (let row = 0; row < currentCard.length; row++) {
      const colIndex = GameScene.BINGO_COLUMNS.indexOf(column);
      if (currentCard[row][colIndex] && !currentCard[row][colIndex].isFree) {
        usedNumbers.add(currentCard[row][colIndex].number);
      }
    }

    // Return available numbers in range
    const available: number[] = [];
    for (let num = range.min; num <= range.max; num++) {
      if (!usedNumbers.has(num)) {
        available.push(num);
      }
    }
    
    return available;
  }

  /**
   * Create the bingo board with random numbers
   */
  createBingoBoard() {
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center the board horizontally

    // Calculate vertical positions based on camera height
    const boardCenterY = this.centerY + 150; // Slightly below center
    const headerY = boardCenterY - (cellSize + cellSpacing) * 2.5;
    const gridStartY = headerY + (cellSize + cellSpacing);

    // Create the BINGO header row
    for (let col = 0; col < 5; col++) {
      const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
      const y = headerY;

      // Create a graphics object for the rounded rectangle
      const graphics = this.add.graphics();

      // Use gradient fill for the header
      graphics.fillGradientStyle(
          0x3066FF, // Top-left: blue
          0x4752FF, // Top-right: blue-purple
          0x5E4DFF, // Bottom-right: more purple
          0x215EFF, // Bottom-left: blue
          1
      );
      graphics.lineStyle(3, 0x00E5AE, 1);

      // Draw rounded rectangle centered at (x, y)
      graphics.fillRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);
      graphics.strokeRoundedRect(x - cellSize/2, y - cellSize/2, cellSize, cellSize, 16);

      // Add letter
      this.add.text(
          x,
          y,
          GameScene.BINGO_COLUMNS[col],
          {
            fontFamily: '"TT Neoris", Arial, sans-serif',
            fontSize: '48px',
            color: '#FFFFFF',
            align: 'center',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 3,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, fill: true }
          }
      ).setOrigin(0.5);
    }

    // Create 5x5 grid of bingo cells (excluding the header row)
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const x = startX + col * (cellSize + cellSpacing) + cellSize / 2;
        const y = gridStartY + row * (cellSize + cellSpacing);

        // Get cell data from generated bingo card
        const cellData = this.bingoCard[row][col];

        // Create cell with proper bingo data
        const cell = new BingoCell(
            this,
            x,
            y,
            cellData.column,
            cellData.isFree ? 0 : cellData.number, // Use 0 for FREE space
            cellData.isFree
        );

        // Hide letter for main board (except FREE space)
        if (!cellData.isFree) {
          cell.letterText.alpha = 0;
        }

        // Initially set scale to 0 for animation
        cell.setScale(0);

        // Add to scene and store reference
        this.bgoCells.push(cell);

        // Create staggered animation for each cell based on row and column
        // This creates a wave-like effect as cells appear
        this.time.delayedCall(150 * row + 50 * col, () => {
          this.tweens.add({
            targets: cell,
            scale: 1,
            duration: 300,
            ease: 'Back.Out'
          });
        });
      }
    }
  }

  /**
   * Start the game timer
   * Equivalent to TimerCoroutine in Unity
   */
  startGameTimer() {
    this.timerManager.start();
  }

  /**
   * Calculate positions for right panel items
   */
  private calculateRightPositions(): { x: number; y: number }[] {
    const positions = [];
    const cellSize = 80;
    const cellSpacing = 10;
    const totalWidth = (cellSize + cellSpacing) * 5 - cellSpacing;
    const startX = this.centerX - totalWidth / 2; // Center horizontally

    // Position between header and timer
    const panelY = this.centerY - 180;

    // Create positions for 5 items (0-4 indices)
    for (let i = 0; i < 5; i++) {
      positions.push({
        x: startX + i * (cellSize + cellSpacing) + cellSize / 2,
        y: panelY
      });
    }

    return positions;
  }

  /**
   * Get position for right panel item by index
   * @param {number} index - Position index
   * @returns {object} Position with x and y coordinates
   */
  public getRightPosition(index: number): { x: number; y: number } {
    if (index >= 0 && index < this.rightPositions.length) {
      return this.rightPositions[index];
    }
    return { x: this.centerX + 100, y: this.centerY };
  }

  private callNextBingoNumber(): CalledNumber | null {
    if (this.availableNumbers.length === 0) {
      // All numbers have been called
      return null;
    }

    // Get the next number from the shuffled pool
    const number = this.availableNumbers.shift()!;
    const column = this.getColumnForNumber(number);

    // Create called number record
    const calledNumber: CalledNumber = {
      column,
      number,
      callOrder: ++this.currentCallOrder,
      timestamp: Date.now()
    };

    // Add to called numbers history
    this.calledNumbers.push(calledNumber);

    return calledNumber;
  }

  private getColumnForNumber(number: number): BingoColumn {
    if (number >= 1 && number <= 15) return 'B';
    if (number >= 16 && number <= 30) return 'I';
    if (number >= 31 && number <= 45) return 'N';
    if (number >= 46 && number <= 60) return 'G';
    if (number >= 61 && number <= 75) return 'O';
    throw new Error(`Invalid bingo number: ${number}`);
  }

  /**
   * Add a new number to the right panel using standard bingo calling
   * Equivalent to addRightItem in Unity
   */
  private addRightItem(): void {
    if (this.gameEnd) return;

    const calledNumber = this.callNextBingoNumber();

    if (!calledNumber) {
      // No more numbers to call - end game
      // this.endGame();
      return;
    }

    // Play sound
    this.sound.play('number-appear');

    // Move existing right numbers down
    this.moveExistingRightItems();

    // Get position for new item
    const position = this.getRightPosition(4);

    // Create new right number with the called number
    const rightNumber = new RightNumber(
        this,
        position.x,
        position.y,
        4,
        calledNumber.column,
        calledNumber.number
    );

    // Add to right panel
    this.rightNumbers.push(rightNumber);

    // Schedule next right item
    this.time.delayedCall(BINGO_CONSTANTS.CALLOUT_DELAY, () => {
      if (!this.gameEnd) {
        this.addRightItem();
      }
    });
  }

  /**
   * Move existing right items down
   */
  moveExistingRightItems() {
    for (const item of this.rightNumbers) {
      // Decrease index
      item.moveToPosition(item.rightIndex - 1);
    }

    // Filter out destroyed items
    this.rightNumbers = this.rightNumbers.filter(item => item.rightIndex >= 0);
  }

  /**
   * Check if a cell matches any right panel item
   * @param {BingoCell} cell - The cell to check
   */
  public checkForMatch(cell: BingoCell): void {
    if (this.gameEnd) return;

    // Find matching right number
    const matchingNumber = this.rightNumbers.find(item => item.name === cell.name);

    if (matchingNumber) {
      // Mark the cell
      cell.mark();

      // Remove any highlight effects (if they exist)
      this.children.list.forEach(child => {
        if (child.type === 'Graphics' &&
            (child as any).x === cell.x - 40 &&
            (child as any).y === cell.y - 40) {
          child.destroy();
        }
      });

      // // Add score based on timer value
      // this.addScore(matchingNumber.getScore());

      this.scoreManager.addPoints(matchingNumber.getScore(), {
        startX: this.centerX,
        startY: 80,
        points: matchingNumber.getScore()
      });

      // Play sound
      this.sound.play('match');

      // Remove the matching number from the array
      this.rightNumbers = this.rightNumbers.filter(item => item !== matchingNumber);

      // Destroy the matching number
      matchingNumber.destroy();

      // Check for winning patterns after marking the cell
      const winResult = this.checkForWinningPatterns();
      console.log('Win result:', winResult);
      if (winResult.hasWon && winResult.pattern === "fullCard") {
        this.handleBingoWin(winResult);
        return;
      }

      // CAN SHEDULE NEXT NUMBER HERE IMMEDIATELY FOR BETTER GAMEPLAY
    }
    else{
      this.sound.play('wrong');

      this.livesManager.deductHeart(
        cell.x,
        cell.y
      );
    }
  }

  /**
   * Handle a bingo win
   */
  private handleBingoWin(winResult: WinResult): void {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;

    // Stop timer
    this.timerManager.stop();

    // Add bonus score based on pattern type
    const bonusScore = DEFAULT_PATTERN_SCORES[winResult.pattern!] || 0;
    // this.addScore(bonusScore);

    this.scoreManager.addPoints(bonusScore, {
      startX: this.centerX,
      startY: 80,
      points: bonusScore
    });

    // Create win announcement
    this.createWinAnnouncement(winResult);

    // Create celebration effects
    this.createCelebrationEffects();

    // Transition to GameEndScene after a brief delay
    this.time.delayedCall(1500, () => {
      this.scene.start('GameEndScene', 
        { 
          score: this.scoreManager.getScore(), 
          winPattern: 'timeout' 
        });
    });
  }

  /**
   * Create win announcement display
   */
  private createWinAnnouncement(winResult: WinResult): void {
    const announcement = this.add.text(
      this.centerX,
      this.centerY - 100,
      PATTERN_DISPLAY_NAMES[winResult.pattern!],
      {
        fontFamily: '"TT Neoris", Arial, sans-serif',
        fontSize: '64px',
        color: '#FFD700',
        align: 'center',
        fontStyle: 'bold',
        stroke: '#000000',
        strokeThickness: 4,
        shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
      }
    ).setOrigin(0.5).setDepth(10);

    // Animate the announcement
    announcement.setScale(0);
    this.tweens.add({
      targets: announcement,
      scale: 1,
      duration: 500,
      ease: 'Back.Out',
      onComplete: () => {
        // Pulse animation
        this.tweens.add({
          targets: announcement,
          scale: { from: 1, to: 1.1 },
          duration: 800,
          yoyo: true,
          repeat: -1,
          ease: 'Sine.easeInOut'
        });
      }
    });
  }

  /**
   * Check for winning patterns on the bingo card
   * Returns the first winning pattern found, if any
   */
  private checkForWinningPatterns(): WinResult {
    // Check full card
    if (this.isFullCardComplete()) {
      const winningCells = this.bingoCard.flat();
      return {
        hasWon: true,
        pattern: 'fullCard',
        winningCells
      };
    }

    // // Check horizontal lines (rows)
    // for (let row = 0; row < 5; row++) {
    //   if (this.isRowComplete(row)) {
    //     return {
    //       hasWon: true,
    //       pattern: 'horizontal',
    //       winningCells: this.bingoCard[row]
    //     };
    //   }
    // }

    // // Check vertical lines (columns)
    // for (let col = 0; col < 5; col++) {
    //   if (this.isColumnComplete(col)) {
    //     const winningCells = this.bingoCard.map(row => row[col]);
    //     return {
    //       hasWon: true,
    //       pattern: 'vertical',
    //       winningCells
    //     };
    //   }
    // }

    // // Check diagonal lines
    // if (this.isDiagonalComplete('main')) {
    //   const winningCells = [];
    //   for (let i = 0; i < 5; i++) {
    //     winningCells.push(this.bingoCard[i][i]);
    //   }
    //   return {
    //     hasWon: true,
    //     pattern: 'diagonal',
    //     winningCells
    //   };
    // }
    // if (this.isDiagonalComplete('anti')) {
    //   const winningCells = [];
    //   for (let i = 0; i < 5; i++) {
    //     winningCells.push(this.bingoCard[i][4 - i]);
    //   }
    //   return {
    //     hasWon: true,
    //     pattern: 'diagonal',
    //     winningCells
    //   };
    // }

    return { hasWon: false };
  }

  // /**
  //  * Check if a specific row is complete
  //  */
  // private isRowComplete(row: number): boolean {
  //   for (let col = 0; col < 5; col++) {
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }

  // /**
  //  * Check if a specific column is complete
  //  */
  // private isColumnComplete(col: number): boolean {
  //   for (let row = 0; row < 5; row++) {
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }

  // /**
  //  * Check if a diagonal is complete
  //  */
  // private isDiagonalComplete(type: 'main' | 'anti'): boolean {
  //   for (let i = 0; i < 5; i++) {
  //     const row = i;
  //     const col = type === 'main' ? i : 4 - i;
  //     const cellData = this.bingoCard[row][col];
  //     const cell = this.findBingoCellByPosition(row, col);
  //     if (!cell || (!cell.marked && !cellData.isFree)) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }

  /**
   * Check if the full card is complete
   */
  private isFullCardComplete(): boolean {
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const cellData = this.bingoCard[row][col];
        const cell = this.findBingoCellByPosition(row, col);
        if (!cell || (!cell.marked && !cellData.isFree)) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * Find a bingo cell by its grid position
   */
  private findBingoCellByPosition(row: number, col: number): BingoCell | null {
    const index = row * 5 + col;
    return this.bgoCells[index] || null;
  }

  /**
   * Find a bingo cell by name
   * @param {string} name - The name of the cell (e.g., "B12")
   * @returns {BingoCell|null} The matching cell or null if not found
   */
  public findBingoCellByName(name: string): BingoCell | null {
    return this.bgoCells.find(cell => cell.name === name) || null;
  }


  /**
   * End the game (called when timer runs out or no more numbers available)
   * Equivalent to when GameEnd is set to true in Unity
   */
  endGame() {
    if (this.gameEnd) return;

    // Set game end flag
    this.gameEnd = true;

    // Stop timer
    this.timerManager.stop();

    // Create timeout announcement
    // this.createTimeoutAnnouncement();

    // Create celebration effects
    this.createCelebrationEffects();

    // Transition to GameEndScene after a brief delay
    this.time.delayedCall(1500, () => {
      this.scene.start('GameEndScene', { score: this.scoreManager.getScore(), winPattern: 'timeout' });
    });
  }

  /**
   * Create timeout announcement when game ends without bingo
   */
  // private createTimeoutAnnouncement(): void {
  //   const announcement = this.add.text(
  //     this.centerX,
  //     this.centerY - 100,
  //     'TIME\'S UP!',
  //     {
  //       fontFamily: '"TT Neoris", Arial, sans-serif',
  //       fontSize: '64px',
  //       color: '#FF6B6B',
  //       align: 'center',
  //       fontStyle: 'bold',
  //       stroke: '#000000',
  //       strokeThickness: 4,
  //       shadow: { offsetX: 3, offsetY: 3, color: '#000000', blur: 5, fill: true }
  //     }
  //   ).setOrigin(0.5).setDepth(10);

  //   // Animate the announcement
  //   announcement.setScale(0);
  //   this.tweens.add({
  //     targets: announcement,
  //     scale: 1,
  //     duration: 500,
  //     ease: 'Back.Out'
  //   });
  // }

  /**
   * Create celebration effects for game end
   * Equivalent to HandleGameEnd in Unity
   */
  createCelebrationEffects() {
    // Play win sound
    this.sound.play('win');

    // Create particles on each cell
    for (const cell of this.bgoCells) {
      // Mark unmarked cells
      if (!cell.marked) {
        cell.mark();
      }

      // Add delayed win particles using seeded random
      this.time.delayedCall(this.rng.between(0, 1000), () => {
        cell.createWinParticles();
      });
    }
  }

  /**
   * Hide game UI elements during countdown
   */
  private hideGameUI(): void {
    this.UIContainer.setVisible(false);
  }
  
  /**
   * Show game UI elements after countdown
  */
 private showGameUI(): void {
   this.UIContainer.setVisible(true);
  }
}
