import * as Phaser from 'phaser';
import type { RadialTimerConfig } from '../types/types';

/**
 * Radial Timer UI Component
 * 
 * Creates a radial timer with a sweeping mask effect that visually "unfills" around a rectangle.
 * 
 * Usage:
 * ```
 * const radialTimer = new RadialTimerUI(scene, {
 *   x: centerX,
 *   y: centerY,
 *   size: 450,
 *   cornerRadius: 16
 * });
 * 
 * radialTimer.create();
 * radialTimer.updateProgress(0.5); // 50% progress
 * ```
 */
export default class RadialTimerUI {
  private scene: Phaser.Scene;
  private config: Required<RadialTimerConfig>;
  
  // UI Elements
  private container?: Phaser.GameObjects.Container;
  private backgroundGraphics?: Phaser.GameObjects.Graphics;
  private progressGraphics?: Phaser.GameObjects.Graphics;
  private maskGraphics?: Phaser.GameObjects.Graphics;

  constructor(scene: Phaser.Scene, config: RadialTimerConfig) {
    this.scene = scene;
    
    // Set default configuration
    this.config = {
      x: config.x,
      y: config.y,
      size: config.size,
      cornerRadius: config.cornerRadius ?? 16,
      backgroundColor: config.backgroundColor ?? 0x333333,
      backgroundAlpha: config.backgroundAlpha ?? 0.6,
      borderWidth: config.borderWidth ?? 8,
      startColor: config.startColor ?? '#ff4d4d',
      endColor: config.endColor ?? '#33ff55'
    };
  }

  /**
   * Create the radial timer UI elements
   */
  public create(parentContainer?: Phaser.GameObjects.Container): void {
    // Create main container
    this.container = this.scene.add.container(0, 0);
    
    // Create background layer
    this.createBackground();
    
    // Create progress layer
    this.createProgress();
    
    // Add to parent container if provided
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }

  /**
   * Update the progress of the radial timer (0-1)
   */
  public updateProgress(progress: number): void {
    if (!this.progressGraphics) return;
    
    // Clamp progress between 0 and 1
    progress = Math.max(0, Math.min(1, progress));
    
    // Clear previous mask if it exists
    if (this.maskGraphics) {
      this.maskGraphics.destroy();
      this.maskGraphics = undefined;
    }

    // Clear the mask from the progress graphics
    this.progressGraphics.clearMask();
    this.progressGraphics.clear();

    if (progress <= 0) return; // Don't draw progress if no time remaining

    // Calculate color based on progress
    const color = Phaser.Display.Color.Interpolate.ColorWithColor(
      Phaser.Display.Color.HexStringToColor(this.config.startColor),
      Phaser.Display.Color.HexStringToColor(this.config.endColor),
      1, progress
    );

    const fillColor = Phaser.Display.Color.GetColor(color.r, color.g, color.b);

    // Create the radial mask
    this.maskGraphics = this.createRadialMask(progress);
    
    // Draw the progress rectangle
    this.progressGraphics.lineStyle(this.config.borderWidth, fillColor, 1);
    this.progressGraphics.strokeRoundedRect(
      this.config.x - this.config.size / 2,
      this.config.y - this.config.size / 2,
      this.config.size,
      this.config.size,
      this.config.cornerRadius
    );
    
    // Apply the mask to create the radial sweep effect
    if (this.maskGraphics) {
      const mask = new Phaser.Display.Masks.GeometryMask(this.scene, this.maskGraphics);
      this.progressGraphics.setMask(mask);
      this.maskGraphics.setVisible(false);
    }
  }

  /**
   * Create the background layer
   */
  private createBackground(): void {
    if (!this.container) return;
    
    this.backgroundGraphics = this.scene.add.graphics();
    this.container.add(this.backgroundGraphics);
    
    // Draw background rounded rectangle
    this.backgroundGraphics.lineStyle(
      this.config.borderWidth, 
      this.config.backgroundColor, 
      this.config.backgroundAlpha
    );
    this.backgroundGraphics.strokeRoundedRect(
      this.config.x - this.config.size / 2,
      this.config.y - this.config.size / 2,
      this.config.size,
      this.config.size,
      this.config.cornerRadius
    );
  }

  /**
   * Create the progress layer
   */
  private createProgress(): void {
    if (!this.container) return;
    
    this.progressGraphics = this.scene.add.graphics();
    this.container.add(this.progressGraphics);
  }

  /**
   * Create the radial mask for the sweep effect
   */
  private createRadialMask(progress: number): Phaser.GameObjects.Graphics {
    this.maskGraphics = this.scene.add.graphics();
    
    // Calculate the sweep angle based on progress (start from top, go clockwise)
    const startAngle = -Math.PI / 2; // Start from top (12 o'clock)
    const sweepAngle = 2 * Math.PI * progress; // How much of the circle to fill
    const endAngle = startAngle + sweepAngle;
    
    // Create the mask shape - a sector that sweeps around the rectangle
    this.maskGraphics.fillStyle(0xffffff, 1);
    this.maskGraphics.beginPath();
    
    // Start from center
    this.maskGraphics.moveTo(this.config.x, this.config.y);
    
    // Create a large radius to ensure we cover the entire rectangle area
    const maskRadius = this.config.size * 0.8; // Large enough to cover the rectangle
    
    // Draw the sector
    this.maskGraphics.arc(this.config.x, this.config.y, maskRadius, startAngle, endAngle, false);
    this.maskGraphics.closePath();
    this.maskGraphics.fillPath();

    return this.maskGraphics;
  }

  /**
   * Set the depth of the timer UI
   */
  public setDepth(depth: number): void {
    if (this.container) {
      this.container.setDepth(depth);
    }
  }

  /**
   * Destroy the timer UI and clean up resources
   */
  public destroy(): void {
    if (this.maskGraphics) {
      this.maskGraphics.destroy();
      this.maskGraphics = undefined;
    }
    
    if (this.progressGraphics) {
      this.progressGraphics.destroy();
      this.progressGraphics = undefined;
    }
    
    if (this.backgroundGraphics) {
      this.backgroundGraphics.destroy();
      this.backgroundGraphics = undefined;
    }
    
    if (this.container) {
      this.container.destroy();
      this.container = undefined;
    }
  }

  /**
   * Get the container for additional positioning/effects
   */
  public getContainer(): Phaser.GameObjects.Container | undefined {
    return this.container;
  }
}
