
// Animal and Color Types
export type AnimalName = 'eagle' | 'koala' | 'wolf' | 'dog';
export type ColorName = 'cyan' | 'green' | 'yellow';

export interface GameEndSceneData {
  score: number;
}

// TicTaps Platform Types
export interface TicTapsMessage {
  type: 'gameReady' | 'gameScore' | 'gameQuit';
  score?: number;
}

// Utility Types
export type Nullable<T> = T | null;


/**
 * Message types sent to TicTaps platform
 */
export type TicTapsMessageType = 'gameReady' | 'gameScore' | 'gameQuit';

/**
 * Message structure for TicTaps communication
 */
export interface TicTapsMessage {
  type: TicTapsMessageType;
  score?: number;
  data?: any;
}


/**
 * Configuration for score display and behavior
 */
export interface ScoreConfig {
  initialScore?: number;
  fontFamily?: string;
  fontSize?: string;
  labelFontSize?: string;
  scoreColor?: string;
  labelColor?: string;
  animationColor?: string;
  animationDuration?: number;
}

/**
 * Configuration for lives display and behavior
 */
export interface LivesConfig {
  initialLives?: number;
}

/**
 * Configuration for score animation
 */
export interface ScoreAnimationConfig {
  startX: number;
  startY: number;
  points: number;
  duration?: number;
  color?: string;
}

/**
 * Configuration for timer behavior and display
 */
export interface TimerConfig {
  duration: number;
  updateInterval?: number;
  fontFamily?: string;
  fontSize?: string;
  normalColor?: string;
  warningColor?: string;
  warningThreshold?: number;
}

/**
 * Timer state information
 */
export interface TimerState {
  timeRemaining: number;
  totalDuration: number;
  isRunning: boolean;
  isFinished: boolean;
  progress: number;
}

/**
 * Configuration for timer bar visual appearance
 */
export interface TimerBarConfig {
  width: number;
  height: number;
  x: number;
  y: number;
  backgroundColor?: number;
  gradientStartColor?: string;
  gradientEndColor?: string;
  cornerRadius?: number;
  showTimerIcon?: boolean;
  showCircularBg?: boolean;
  circleRadius?: number;
  circleBorderColor?: number;
  circleBorderWidth?: number;
}

/**
 * Configuration for radial timer visual appearance
 */
export interface RadialTimerConfig {
  x: number;
  y: number;
  size: number;
  cornerRadius?: number;
  backgroundColor?: number;
  backgroundAlpha?: number;
  borderWidth?: number;
  startColor?: string;
  endColor?: string;
}