### In Game Scenes (e.g., FingerFrenzy GameScene)

```typescript
// Socket client is available via game registry
const gameConfig = this.registry.get("gameConfig") as FingerFrenzyConfig;
this.socketClient = gameConfig?.socketClient || null;

// Send game start event
if (this.socketClient && this.socketClient.isConnected()) {
  this.socketClient.startGame("finger-frenzy");
}

// Send game action events (like correct/incorrect clicks)
this.socketClient.sendGameEvent("correctClick", {
  blockIndex: 5,
  reactionTime: 450,
  gameType: "finger-frenzy",
});

// Send game end event
this.socketClient.endGame("finger-frenzy");
```
